# API 接口文档

本文档描述了基于 FastAPI 的 AutoGen SSE API 服务器的接口。

## 1. SSE 连接端点

此端点用于建立一个 Server-Sent Events (SSE) 连接，以便实时接收来自服务器的消息。

-   **Path:** `/api/sse`
-   **Method:** `GET`
-   **Description:** 客户端通过此接口与服务器建立一个长连接，服务器将通过此连接推送事件。

### 请求参数

-   **Query Parameters:**
    -   `session_id` (string, 可选): 会话 ID。如果提供，则尝试加入现有会话；如果不提供，则会创建一个新的会话 ID 并返回。

### 响应

-   **Content-Type:** `text/event-stream`
-   **Streamed Events:**
    一旦连接建立，服务器会首先发送一个连接确认消息，然后根据后端智能体交互的进展，推送不同类型的事件。

    1.  **连接建立确认:**
        ```json
        {
            "type": "connection_established",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "is_terminated": false,
            "message": {"content": "SSE连接已建立"}
        }
        ```

    2.  **用户消息 (由 `/api/chat` 触发后，回显给所有连接到该 session_id 的客户端):**
        ```json
        {
            "type": "user_message",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "message": {"content": "用户输入的消息"}
        }
        ```
        *(这个消息是在 process_chat 开始时发送的，用于告知客户端用户消息已被接收)*

    3.  **系统消息:**
        ```json
        {
            "type": "system_message",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "message": "系统提示信息，例如：正在处理您的请求..."
        }
        ```

    4.  **请求用户输入:**
        当智能体需要用户进一步输入时，会发送此事件。
        ```json
        {
            "type": "user_input_requested",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "data": {
                "prompt": "智能体请求用户输入的问题或提示"
            }
        }
        ```

    5.  **智能体消息:**
        智能体在内部讨论或执行任务时产生的消息。
        ```json
        {
            "type": "agent_message",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "data": {
                "sender": "智能体名称 (例如 Planner, Monitor)",
                "content": "智能体的回复或消息内容"
            }
        }
        ```
        *也可能包含其他 AutoGen `event.type` 和 `event.data` 的原始事件。*


    6.  **对话结束:**
        当整个对话流程结束时发送。
        ```json
        {
            "type": "conversation_end",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "is_terminated": true,
            "stop_reason": "对话终止的原因 (例如 max_messages_reached, text_mention_termination)"
        }
        ```

    7.  **错误消息:**
        如果在处理过程中发生错误。
        ```json
        {
            "type": "error",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "is_terminated": true,
            "error": "错误信息描述",
            "traceback": "详细的错误堆栈信息"
        }
        ```

## 2. 聊天 API 端点

此端点用于向后端发送用户消息，并启动或继续一个聊天会话。

-   **Path:** `/api/chat`
-   **Method:** `POST`
-   **Description:** 用户通过此接口发送消息。如果会话当前正等待用户输入，则此消息将作为输入提供给等待的智能体。否则，将启动新的聊天处理流程。消息处理是异步的，结果通过已建立的 SSE 连接返回。

### 请求体

-   **Content-Type:** `application/json`
-   **Body:**
    ```json
    {
        "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", // (可选) 现有会话 ID
        "message": "用户的输入消息内容"                     // (必选) 用户发送的消息
    }
    ```

### 响应

-   **Content-Type:** `application/json`

    1.  **输入被接受 (会话正在等待用户输入):**
        ```json
        {
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "status": "input_accepted"
        }
        ```

    2.  **对话开始 (新消息启动了处理流程):**
        ```json
        {
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "status": "conversation_started"
        }
        ```

    3.  **错误 - 无 SSE 连接:**
        如果指定的 `session_id` 没有活跃的 SSE 连接。
        ```json
        {
            "error": "此会话没有活跃的SSE连接",
            "session_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
            "status": "error"
        }
        ```

    4.  **错误 - 其他:**
        处理请求时发生的其他错误。
        ```json
        {
            "error": "具体的错误信息",
            "traceback": "详细的错误堆栈信息",
            "status": "error"
        }
        ```

## 3. 健康检查端点

用于检查 API 服务器的健康状态。

-   **Path:** `/api/health`
-   **Method:** `GET`
-   **Description:** 返回服务器当前的健康状态和时间戳。

### 请求参数

无

### 响应

-   **Content-Type:** `application/json`
-   **Success Response:**
    ```json
    {
        "status": "healthy",
        "timestamp": **********.000  // Unix时间戳
    }
    ```
