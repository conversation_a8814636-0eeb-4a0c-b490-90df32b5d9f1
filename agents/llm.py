import yaml
import os
from autogen_core.models import ChatCompletionClient

def get_llm(model_name):
    model_config = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/model_config.yaml")
    with open(model_config, "r") as f:
        model_config = yaml.safe_load(f)
    model_client = ChatCompletionClient.load_component(model_config["text_models"][model_name])
    return model_client