from datetime import datetime

from autogen_core.memory import MemoryContent, MemoryMimeType, ListMemory

from autogen_agentchat.agents import Assistant<PERSON>gent

from langchain_community.document_loaders import TextLoader

try:
    from tools import get_fk_flag_tool, retrieve_domain_info_tool
except ImportError:
    from ..tools import get_fk_flag_tool, retrieve_domain_info_tool
from .llm import get_llm
try:
    from prompts import get_prompt
except ImportError:
    from ..prompts import get_prompt

class Memory:
    def __init__(self,  file_path):
        self.file_path = file_path

    def load_documents(self):    # 加载文档
        loader = TextLoader(self.file_path)
        return loader.load()

    async def get_memory(self):
        memory = ListMemory()
        documents = self.load_documents()
        # 从文档中提取内容并创建记忆对象
        document_content = documents[0].page_content
        document_metadata = documents[0].metadata
        await memory.add(MemoryContent(
            content=document_content,
            mime_type=MemoryMimeType.TEXT,
            metadata=document_metadata
        ))
        return memory


def create_planner_agent(model_name):
    planner_agent = AssistantAgent(
        "planner_agent",
        model_client=get_llm(model_name),
        system_message=get_prompt("planner"),
    )
    return planner_agent

def create_rag_agent(model_name):
    rag_agent = AssistantAgent(
        "rag_agent",
        model_client=get_llm(model_name),
        system_message=get_prompt("rag"),
        tools=[retrieve_domain_info_tool]
    )
    return rag_agent

def create_monitor_agent(model_name, mcp_tools, memory):
    today_str = datetime.now().strftime("%Y%m%d")
    # today_str = "20250422"  # 指定查询日期，实验用
    monitor_agent = AssistantAgent(
        "monitor_agent",
        model_client=get_llm(model_name),
        system_message=get_prompt("monitor").format(today_str=today_str),
        tools = mcp_tools + [get_fk_flag_tool],
        memory = [memory]
    )
    return monitor_agent

def create_chat_agent(model_name):
    chat_agent = AssistantAgent(
        "chat_agent",
        model_client=get_llm(model_name),
        system_message=get_prompt("chat"),
    )
    return chat_agent
