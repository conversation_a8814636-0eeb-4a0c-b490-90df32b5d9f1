import os
import asyncio
import traceback
from typing import Sequence
import time
import json
import re

from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.teams import Selector<PERSON>roupChat
from autogen_agentchat.ui import Console
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.agents import UserProxyAgent

from autogen_core.memory import MemoryContent, MemoryMimeType, ListMemory
from autogen_ext.tools.mcp import mcp_server_tools, SseServerParams

from langchain_community.document_loaders import TextLoader

try:
    from agents import create_planner_agent, create_monitor_agent, create_rag_agent, create_chat_agent
except ImportError:
    from ..agents.agents import create_planner_agent, create_monitor_agent, create_rag_agent, create_chat_agent
try:
    from agents.llm import get_llm
except ImportError:
    from ..agents.llm import get_llm

class Memory:
    def __init__(self,  file_path):
        self.file_path = file_path

    def load_documents(self):    # 加载文档
        loader = TextLoader(self.file_path)
        return loader.load()

    async def get_memory(self):
        memory = ListMemory()
        documents = self.load_documents()
        # 从文档中提取内容并创建记忆对象
        document_content = documents[0].page_content
        document_metadata = documents[0].metadata
        await memory.add(MemoryContent(
            content=document_content,
            mime_type=MemoryMimeType.TEXT,
            metadata=document_metadata
        ))
        return memory


def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str | None:
    if messages[-1].source == "user":
        return "planner_agent"

    if messages[-1].source == "planner_agent":
        if "rag_agent" in messages[-1].to_text():
            return "rag_agent"
        elif "monitor_agent" in messages[-1].to_text():
            return "monitor_agent"
        else:
            return "chat_agent"

    if messages[-1].source in ("rag_agent", "monitor_agent"):
        if "请您补充" in messages[-1].to_text():
           return "user_proxy"
        return messages[-1].source

    if messages[-1].source == "user_proxy":
        return messages[-2].source
    else:
        return "chat_agent"


def extract_conclusion(text):
    match = re.search(r'【结论】\s*(.*?)\s*TERMINATE', text, re.DOTALL)
    if match:
        return match.group(1).strip()
    else:
        return text

# 本地调试用
async def main(task):
    # mcp工具
    # server_params = SseServerParams(url="http://**************:8080/mcp/sse", headers={"Authorization": "Bearer token"})
    header = {
        "sessionId": "{agent_name}_{timestamp}".format(agent_name="{agent_name}", timestamp=time.time())
    }
    server_params = SseServerParams(url="https://bmlagent.sankuai.com/mcp/Dispatch/DispatchDefaultServer/sse",
                                    headers=header)
    mcp_tools = await mcp_server_tools(server_params)

    # 长期记忆
    memory_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "./memory/businessLine.md")
    memory = await Memory(memory_file).get_memory()

    # agent
    planner_agent = create_planner_agent("deepseek-v3-friday")
    monitor_agent = create_monitor_agent("deepseek-v3-friday", mcp_tools, memory)
    rag_agent = create_rag_agent("deepseek-v3-friday")
    chat_agent = create_chat_agent("deepseek-v3-friday")
    # planner_agent = create_planner_agent("qwen3-235b-a22b-meituan")
    # monitor_agent = create_monitor_agent("qwen3-235b-a22b-meituan", mcp_tools, memory)
    # rag_agent = create_rag_agent("qwen3-235b-a22b-meituan")
    # chat_agent = create_chat_agent("qwen3-235b-a22b-meituan")
    user_proxy = UserProxyAgent("user_proxy", input_func=input)


    # 终止条件
    text_termination = TextMentionTermination("TERMINATE")
    max_termination = MaxMessageTermination(20)
    termination = text_termination | max_termination

    # team
    team = SelectorGroupChat(
        participants=[planner_agent, monitor_agent, rag_agent, chat_agent, user_proxy],
        model_client=get_llm("deepseek-v3-friday"),
        termination_condition=termination,
        selector_func=selector_func)

    await Console(team.run_stream(task=task))


async def func_template(**args):
    """
    对外暴露函数模板
    输入:
        **args: agent执行相关入参
    输出:
        json: 执行结果
    """
    response_data = {
        "code": 500,  # 状态码
        "output": [],  # 控制台输出日志
        "stop_reason": "",  # 终止原因
        "message": ""  # 异常or备注信息填充
    }
    try:
        # 1. 取参数，构造调用参数，按需自行读取参数
        task = args.get("task")  # 样例

        # 2. 调用初始化
        ## mcp server连接初始化
        # mcp工具
        header = {
            "sessionId": "{agent_name}_{timestamp}".format(agent_name="{agent_name}", timestamp=time.time())
        }
        server_params = SseServerParams(url="https://bmlagent.sankuai.com/mcp/Dispatch/DispatchDefaultServer/sse",
                                        headers=header)
        mcp_tools = await mcp_server_tools(server_params)

        # 长期记忆
        memory_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "./memory/businessLine.md")
        memory = await Memory(memory_file).get_memory()

        # agent
        planner_agent = create_planner_agent("deepseek-v3-friday")
        monitor_agent = create_monitor_agent("deepseek-v3-friday", mcp_tools, memory)
        rag_agent = create_rag_agent("deepseek-v3-friday")
        chat_agent = create_chat_agent("deepseek-v3-friday")

        # 终止条件
        text_termination = TextMentionTermination("TERMINATE")
        max_termination = MaxMessageTermination(20)
        termination = text_termination | max_termination

        # team
        team = SelectorGroupChat(
            participants=[planner_agent, monitor_agent, rag_agent, chat_agent],
            model_client=get_llm("deepseek-v3-friday"),
            termination_condition=termination,
            selector_func=selector_func)

        # 3. 调用 await team.run(task=task)
        message = await team.run(task=task)
        # 4. 解析run返回日志
        task_result = []
        for single_message in message.messages:
            task_result.append(
                {
                    "source": str(single_message.source),
                    "models_usage": str(single_message.models_usage),
                    "metadata": str(single_message.metadata),
                    "content": str(single_message.content),
                    "type": str(single_message.type)
                }
            )
        stop_reason = message.stop_reason
        ## output内建议仅保留原始记录

        # 5. 构造返回结果
        response_data['output'] = task_result
        response_data['stop_reason'] = stop_reason
        last_message = task_result[-1]
        if last_message and "TERMINATE" in last_message['content']:
            response_data['code'] = 1000
        ## 此处可以插入自定义判断逻辑，根据自定义判断结果填入状态码与Message信息

        # 6. 返回日志结果
        return response_data
    except Exception as e:
        ## 预期内的异常可以放入Message里返回
        response_data['code'] = 500
        response_data['message'] = str(traceback.format_exc())
    finally:
        return response_data


def func_schedule_agent(context, **args):
    """
    	入参: context python4java通信上下文，固定入参
      			**args 如果是固定的输入，直接指定具体的参数即可
    """
    # 1. 取参
    task = args.get("task")

    # 2. agent执行
    result = asyncio.run(func_template(task=task))
    tags = {
        "code":str(result['code'])
    }
    # 3. 指标上报
    context.log_metric("Agent_RunState", tags, 1)
    return result


if __name__ == "__main__":
    # task = input("请输入：")
    # task = "20250418日到综机审未返回量级较多，什么原因"
    # task = "昨天到综机审未返回量级较多，什么原因"
    # task = "到餐机审未返回量级较多，什么原因"
    # task = "到餐商品列表采集情况是咋样？"
    # task = "到餐日历票是否都采集完？"
    # task = "到餐打包门详采集未返回量级较多，什么原因？"
    # task = "job6925有封禁吗？"
    # task = "采集需求包含哪些内容？"
    # task = "渠道管理有什么功能？"
    # task = "采集状态回收是什么意思？"
    # task = "你好呀"
    # task = "到餐日历票20250425采集情况？"

    task = "到综自动补贴的采集情况怎么样？" # 需求描述没有相关信息，需要用户补充输入信息
    # task = "到综高优大循环采集情况怎么样？"  # 需求描述有的任务
    # task = "到餐今日封禁情况"  # 没描述具体需求，需要用户补充
    # task = "J+今日采集情况如何？" # 补充业务线
    # task = "我想知道到餐业务线有哪些采集需求" # 意图识别优化

    asyncio.run(main(task))

    # result = asyncio.run(func_template(task=task))
    # with open("./result.json", "w", encoding="utf-8") as f:
    #     f.write(json.dumps(result, ensure_ascii=False))
