<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoGen 聊天界面</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .header {
            background-color: #4285f4;
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .user {
            background-color: #e1f5fe;
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 0;
        }
        
        .agent {
            background-color: #f1f1f1;
            margin-right: auto;
            border-bottom-left-radius: 0;
        }
        
        .system {
            background-color: #fff8e1;
            margin: 10px auto;
            text-align: center;
            font-style: italic;
            font-size: 0.9em;
        }
        
        .error {
            background-color: #ffebee;
            color: #d32f2f;
            margin: 10px auto;
            text-align: center;
        }
        
        .agent-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #4285f4;
        }
        
        .input-area {
            display: flex;
            padding: 10px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 16px;
            outline: none;
        }
        
        button {
            margin-left: 10px;
            padding: 0 20px;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2a75f3;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f7f7f7;
            padding: 8px;
            border-radius: 4px;
            overflow: auto;
            margin: 5px 0;
        }
        
        .status {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="header">
            AutoGen 多智能体对话系统
        </div>
        
        <div class="messages" id="messages"></div>
        
        <div class="input-area">
            <textarea id="user-input" rows="3" placeholder="请输入您的问题..."></textarea>
            <button id="send-button">发送</button>
        </div>
        
        <div class="status" id="status"></div>
    </div>

    <script>
        let sessionId = null;
        let eventSource = null;
        const messagesContainer = document.getElementById('messages');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const statusElement = document.getElementById('status');
        
        // 初始化连接
        function initConnection() {
            // 关闭现有连接
            if (eventSource) {
                eventSource.close();
            }
            
            // 获取或创建会话ID
            if (!sessionId) {
                sessionId = localStorage.getItem('autogen_session_id');
                if (!sessionId) {
                    sessionId = generateUUID();
                    localStorage.setItem('autogen_session_id', sessionId);
                }
            }
            
            // 创建SSE连接
            const url = `/api/sse?session_id=${sessionId}`;
            eventSource = new EventSource(url);
            
            // 连接打开
            eventSource.onopen = function() {
                updateStatus('已连接到服务器');
            };
            
            // 接收消息
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleServerMessage(data);
            };
            
            // 错误处理
            eventSource.onerror = function(error) {
                updateStatus('SSE连接错误，尝试重新连接...');
                setTimeout(initConnection, 3000);
            };
        }
        
        // 处理从服务器接收的消息
        function handleServerMessage(data) {
            const messageType = data.type;
            
            switch (messageType) {
                case 'connection_established':
                    updateStatus(`连接已建立，会话ID: ${data.session_id}`);
                    break;
                
                case 'agent_message':
                    if (data.message) {
                        const agentName = data.message.source || 'agent';
                        const content = data.message.content || '';
                        addMessage(agentName, content, 'agent');
                    }
                    break;
                
                case 'user_input_required':
                    updateStatus('等待用户输入...');
                    enableUserInput(true);
                    if (data.message && typeof data.message === 'string') {
                        addMessage('系统', data.message, 'system');
                    }
                    break;
                
                case 'system_message':
                    if (data.message && data.message.content) {
                        addMessage('系统', data.message.content, 'system');
                    }
                    break;
                
                case 'error':
                    addMessage('错误', data.error || '发生未知错误', 'error');
                    enableUserInput(true);
                    break;
                
                case 'conversation_end':
                    updateStatus(`对话结束，原因: ${data.stop_reason || '未知'}`);
                    enableUserInput(true);
                    break;
                
                default:
                    console.log('未处理的消息类型:', messageType, data);
            }
            
            if (data.is_terminated) {
                updateStatus('对话已终止，可以开始新对话');
                // 可以选择创建新的会话ID
                // sessionId = generateUUID();
                // localStorage.setItem('autogen_session_id', sessionId);
            }
        }
        
        // 添加消息到聊天界面
        function addMessage(sender, content, type) {
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            
            if (type === 'agent') {
                const nameElement = document.createElement('div');
                nameElement.className = 'agent-name';
                nameElement.textContent = sender;
                messageElement.appendChild(nameElement);
            }
            
            // 检查是否有代码块
            const formattedContent = formatMessageContent(content);
            messageElement.innerHTML += formattedContent;
            
            messagesContainer.appendChild(messageElement);
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 格式化消息内容，处理代码块等
        function formatMessageContent(content) {
            if (!content) return '';
            
            // 替换可能的代码块（使用```标记的）
            return content.replace(/```([\s\S]*?)```/g, function(match, code) {
                return `<pre>${code}</pre>`;
            });
        }
        
        // 更新状态显示
        function updateStatus(message) {
            statusElement.textContent = message;
        }
        
        // 启用/禁用用户输入
        function enableUserInput(enabled) {
            userInput.disabled = !enabled;
            sendButton.disabled = !enabled;
        }
        
        // 发送用户消息
        async function sendUserMessage() {
            const message = userInput.value.trim();
            if (!message) return;
            
            // 添加用户消息到聊天
            addMessage('用户', message, 'user');
            
            // 清空输入框并禁用输入
            userInput.value = '';
            enableUserInput(false);
            updateStatus('正在处理...');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    addMessage('错误', data.error, 'error');
                    enableUserInput(true);
                }
                
                updateStatus(`状态: ${data.status || '未知'}`);
            } catch (error) {
                addMessage('错误', `无法发送消息: ${error.message}`, 'error');
                enableUserInput(true);
            }
        }
        
        // 生成UUID作为会话ID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // 按钮点击事件
        sendButton.addEventListener('click', sendUserMessage);
        
        // 回车键发送
        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendUserMessage();
            }
        });
        
        // 页面加载时初始化连接
        document.addEventListener('DOMContentLoaded', initConnection);
    </script>
</body>
</html>
