import os
import asyncio
import time
import json
import traceback
import uuid
from typing import Dict, Any, Optional, List

from fastapi import Fast<PERSON><PERSON>, Request, BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse
import uvicorn

from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_core import CancellationToken

from main.team_provider import DefaultTeamProvider, TeamProvider

class SSEMessageHandler:
    """处理SSE消息的类"""
    def __init__(self, request: Request):
        self.request = request
        self.queue = asyncio.Queue()
        self.is_closed = False
        
    async def stream(self):
        """生成SSE流"""
        try:
            while not self.is_closed:
                message = await self.queue.get()
                if message is None:  # 终止信号
                    break
                    
                yield f"data: {json.dumps(message)}\n\n"
        except asyncio.CancelledError:
            self.is_closed = True
            
    async def send_message(self, message: Dict[str, Any]):
        """向SSE流发送消息"""
        if not self.is_closed:
            await self.queue.put(message)
        
    async def close(self):
        """关闭SSE流"""
        self.is_closed = True
        await self.queue.put(None)


class SessionManager:
    """管理会话状态"""
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}  # session_id -> {team, state_json}
        
    def create_session(self, session_id: str):
        """创建新会话或重置现有会话"""
        self.sessions[session_id] = {"team": None, "state_json": None, "sse_handler": None}
        return session_id
        
    def get_session(self, session_id: str):
        """获取会话数据，不存在则创建"""
        if session_id not in self.sessions:
            self.create_session(session_id)
        return self.sessions[session_id]
        
    async def save_team_state(self, session_id: str, team: Any): 
        """为会话保存团队状态（异步） - 此方法可能被废弃或由TeamProvider接管"""
        print(f"[SessionManager.save_team_state] called for {session_id}. This should ideally be handled by TeamProvider.")
        return None 


class UserInputManager:
    """管理用户输入的异步等待和继续"""
    def __init__(self):
        self.waiting_inputs = {}  # session_id -> Future
        
    async def wait_for_input(self, session_id: str):
        """等待用户为指定会话提供输入"""
        future = asyncio.Future()
        self.waiting_inputs[session_id] = future
        return await future
        
    async def provide_input(self, session_id: str, user_input: str):
        """提供用户输入并解除等待"""
        if session_id in self.waiting_inputs:
            future = self.waiting_inputs.pop(session_id)
            future.set_result(user_input)
            return True
        return False


# 创建应用实例
app = FastAPI(title="AutoGen SSE API Server")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
sse_handlers: Dict[str, SSEMessageHandler] = {}
session_manager = SessionManager()
input_manager = UserInputManager()

# # Initialize TeamProvider instance
# team_provider: TeamProvider = DefaultTeamProvider()

# termination 和 cancellation 字典现在由 DefaultTeamProvider 内部管理部分，
# 但API层面可能仍需追踪，尤其 cancellationToken 需要传递
session_cancellation: Dict[str, CancellationToken] = {} 

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

@app.get("/")
def root():
    """重定向到前端页面"""
    return RedirectResponse(url="/static/index.html", status_code=302)


async def message_processor(event, sse_handler: SSEMessageHandler, session_id: str):
    """处理并发送智能体消息到SSE流"""
    if isinstance(event, (BaseAgentEvent, BaseChatMessage)):
        message_data = {}
        
        # 尝试将事件转换为字典
        if hasattr(event, "to_dict"):
            message_data = event.to_dict()
        else:
            # 手动构造消息数据
            message_data = {
                "source": str(event.source),
                "content": str(event.content) if hasattr(event, "content") else "",
                "type": str(event.type) if hasattr(event, "type") else "unknown"
            }
        
        # 发送消息
        await sse_handler.send_message({
            "type": "agent_message",
            "session_id": session_id,
            "is_terminated": False,
            "message": message_data
        })


async def process_chat(user_input: str, session_id: str, sse_handler: SSEMessageHandler, 
                       session: Dict[str, Any]): 
    """处理聊天逻辑 - 重构为使用TeamProvider"""
    try:
        team_provider = DefaultTeamProvider(session_id=session_id)
        # 1. 创建team
        team, team_participants = await team_provider.create_team()
        team_provider.team = team
        session["team"] = team 

        # 2. 设置user_proxy_agent的input函数
        await team_provider.set_user_input_func(sse_handler, session_manager, team_participants, team)

        # 3. 准备cancellation token
        team_provider.cancellation_token = CancellationToken()

        # 4. 获取session会话，并加载team
        session_state_json_str: Optional[str] = None
        try:
            with open(team_provider.get_state_json_path(), "r", encoding="utf-8") as f:
                all_states = json.load(f)
                session_state_json_str = all_states.get(session_id)
        except FileNotFoundError:
            pass # Handled below
        except json.JSONDecodeError:
            print(f"Error decoding state file for session {session_id}. Starting new session.")
        is_loaded_from_state = False
        if session_state_json_str:
            is_loaded_from_state = True
            session_state_data = json.loads(session_state_json_str) if isinstance(session_state_json_str, str) else session_state_json_str
            await team.load_state(session_state_data)

        # 5. 将用户输入添加到team的消息队列
        if user_input and is_loaded_from_state:
            team_provider.put_message_to_queue(user_input)

        # 6. Run conversation using TeamProvider
        task_result = await team_provider.run_team_conversation(
            team=team,
            initial_user_input=user_input,
            sse_handler=sse_handler,
            session_manager=session_manager, 
            is_loaded_from_state=is_loaded_from_state
        )

        # 7. Handle conversation end
        stop_reason = "unknown"
        is_finished = False
        if task_result is not None:
            stop_reason = task_result.stop_reason or "completed"
            is_finished = True
        else:
            is_finished = False
        if is_finished:
            await team_provider.cleanup_session_resources(session_id=session_id, session_manager=session_manager)
            await sse_handler.send_message({
                "type": "conversation_end",
                "session_id": session_id,
                "is_terminated": True,
                "stop_reason": stop_reason,
                "is_success": True
            })

    except asyncio.CancelledError:
        print(f"Team run_stream for session {session_id} was cancelled externally (e.g., SSE disconnect).")
        if sse_handler and not sse_handler.is_closed:
            await sse_handler.send_message({
                "type": "conversation_end",
                "session_id": session_id,
                "is_terminated": True,
                "stop_reason": "Cancelled: Connection Closed or External Interrupt"
            })
    except Exception as e:
        print(f"Error in process_chat for session {session_id}: {e}")
        if sse_handler and not sse_handler.is_closed:
            await sse_handler.send_message({
                "type": "error",
                "session_id": session_id,
                "is_terminated": True,
                "error": str(e),
                "traceback": traceback.format_exc()
            })
    finally:
        pass 


@app.get("/api/sse")
async def sse(request: Request, session_id: Optional[str] = None):
    """SSE连接端点"""
    if not session_id:
        session_id = str(uuid.uuid4())
        session_manager.create_session(session_id)
    
    # 创建SSE处理器
    sse_handler = SSEMessageHandler(request)
    sse_handlers[session_id] = sse_handler
    
    # 保存到会话
    session = session_manager.get_session(session_id)
    session["sse_handler"] = sse_handler
    
    # 发送连接成功消息
    await sse_handler.send_message({
        "type": "connection_established",
        "session_id": session_id,
        "is_terminated": False,
        "message": {"content": "SSE连接已建立", "session_id": session_id} 
    })
    
    return StreamingResponse(
        sse_handler.stream(),
        media_type="text/event-stream"
    )


@app.post("/api/chat")
async def chat(request: Request, background_tasks: BackgroundTasks):
    """聊天API端点"""
    try:
        data = await request.json()
        session_id = data.get("session_id")
        user_input = data.get("message")
        
        if not session_id:
            session_id = str(uuid.uuid4())
            print(f"New session_id generated by /api/chat: {session_id}")
        
        # Get SSE handler; if not found, client needs to establish SSE connection first.
        sse_handler = sse_handlers.get(session_id)
        if not sse_handler:
            return {
                "error": "此会话没有活跃的SSE连接。请先连接到 /api/sse?session_id=<session_id>", 
                "session_id": session_id,
                "status": "error_no_sse_connection"
            }
            
        # 
        session = session_manager.get_session(session_id) 
        session["sse_handler"] = sse_handler 
        
        background_tasks.add_task(
            process_chat, 
            user_input, 
            session_id, 
            sse_handler, 
            session
        )
        
        return {"session_id": session_id, "status": "conversation_processing_started", "message": "聊天请求已接收，正在后台处理。"}
    
    except Exception as e:
        return {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "status": "error"
        }


@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}


def run_server(host="0.0.0.0", port=8000):
    """启动服务器"""
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main.api_server:app", host="0.0.0.0", port=8000, reload=True, reload_dirs=["main", "agents"])
