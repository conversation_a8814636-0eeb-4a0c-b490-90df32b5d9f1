from typing import Dict, Any, Optional, List, Tuple, Callable
from collections import deque
import os
import asyncio
import json
from autogen_agentchat.teams import SelectorGroupChat, BaseGroupChat
from autogen_core import CancellationToken
from base_team import TeamProvider

# Forward declarations or import types carefully to avoid circular dependencies initially
# These will be properly imported in DefaultTeamProvider or when api_server is refactored
class SSEMessageHandler:
    async def send_message(self, message: Dict[str, Any]): ...

class SessionManager:
    def get_session(self, session_id: str) -> Dict[str, Any]: ...
    async def save_team_state(self, team: SelectorGroupChat): ... # This might change

class UserInputManager:
    async def wait_for_input(self, session_id: str) -> str: ...
    def create_sse_input_func(self, session_id: str, queue: Any, team: Optional[SelectorGroupChat] = None):
        # This is a placeholder, the actual sse_input_func creation will be more complex
        # and likely reside within the TeamProvider implementation or be passed in.
        async def sse_input(prompt: str, cancellation_token=None):
            return "user_input_placeholder"
        return sse_input
# Additional imports for DefaultTeamProvider
import time
import traceback

from autogen_agentchat.agents import UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, MaxMessageTermination, TextMentionTermination

from autogen_ext.tools.mcp import mcp_server_tools, SseServerParams

from ..agents.agents import create_planner_agent, create_monitor_agent, create_rag_agent, create_chat_agent
from ..agents.llm import get_llm
from .run_multiagent import Memory, selector_func

class RealTeamProvider(TeamProvider):
    """Default implementation of TeamProvider using existing logic."""

    def __init__(self, context: None = None, memory_file_path: Optional[str] = None, default_llm_name: str = "deepseek-v3-friday", session_id: Optional[str] = None):
        self.memory_file_path = memory_file_path or os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "./memory/businessLine.md")
        self.default_llm_name = default_llm_name
        self.session_cancellation_tokens: Dict[str, CancellationToken] = {}
        self.session_external_terminations: Dict[str, ExternalTermination] = {}
        self._message_queue = deque()
        self.team: Optional[BaseGroupChat] = None
        self.cancellation_token: Optional[CancellationToken] = None
        self.session_id: Optional[str] = session_id
        self.context = context

    async def _create_base_team(
        self
    ) -> Tuple[BaseGroupChat, list]:
        header = {
            "sessionId": f"sse_{self.session_id}_{time.time()}"
        }
        server_params = SseServerParams(
            url="https://bmlagent.sankuai.com/mcp/Dispatch/DispatchDefaultServer/sse", 
            headers=header
        )
        mcp_tools = await mcp_server_tools(server_params)
        
        memory_content = await Memory(self.memory_file_path).get_memory()
        
        planner_agent = create_planner_agent(self.default_llm_name)
        monitor_agent = create_monitor_agent(self.default_llm_name, mcp_tools, memory_content)
        rag_agent = create_rag_agent(self.default_llm_name)
        chat_agent = create_chat_agent(self.default_llm_name)

        user_proxy = UserProxyAgent("user_proxy", input_func=self._sse_input_replace)

        text_termination = TextMentionTermination("TERMINATE")
        max_termination = MaxMessageTermination(20)

        termination_condition = text_termination | max_termination
        
        team_participants = [planner_agent, monitor_agent, rag_agent, chat_agent, user_proxy]
        
        team = SelectorGroupChat(
            participants=team_participants,
            model_client=get_llm(self.default_llm_name),
            termination_condition=termination_condition,
            selector_func=selector_func
        )
        return team, team_participants
    
    async def _sse_input_replace(self, prompt, cancellation_token):
        return ""

    async def create_team(
        self,
    ) -> Tuple[BaseGroupChat, list]:
        team, team_participants = await self._create_base_team()
        return team, team_participants

def get_path(context, **args):
    import os
    absolute_path = os.getcwd()
    current_file_path = os.path.abspath(__file__)
    relative_path = os.path.relpath(current_file_path, absolute_path)
    # 去掉.py后缀并替换路径分隔符为点
    if relative_path.endswith('.py'):
        relative_path = relative_path[:-3]
    module_path = relative_path.replace(os.sep, '.')
    return {
        "module_name": module_path
    }
    # agent_impl_type: custom_agent
    # 全流程节点定义：{}
    # 