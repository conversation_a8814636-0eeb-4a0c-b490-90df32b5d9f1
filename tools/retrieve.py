import os
import yaml
from langchain_openai import OpenAIEmbeddings
from langchain_chroma import Chroma

async def retrieve_domain_info_tool(task: str):
    """检索task相关领域知识"""
    model_config = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/model_config.yaml")
    with open(model_config, "r") as f:
        model_config = yaml.safe_load(f)

    emb_config = model_config["emb_models"]["text-embedding-miffy-002"]

    embeddings = OpenAIEmbeddings(
        model=emb_config["model"],
        api_key=emb_config["api_key"],
        base_url=emb_config["base_url"],
        check_embedding_ctx_length=emb_config["check_embedding_ctx_length"],
        chunk_size=emb_config["chunk_size"]
    )

    persist_directory = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "./memory/domain_knowledge_miffy002_chroma")

    # 1. 检索QA
    qa_vector_store = Chroma(
        collection_name="FAQ",
        embedding_function=embeddings,
        persist_directory=persist_directory,
    )

    query_results = qa_vector_store.similarity_search_with_score(task, k=1)

    if query_results[0][1] < 0.2:  # 有QA，则直接返回QA结果
        return query_results[0][0].metadata['answer']

    # 2. 检索领域知识文档
    vector_store = Chroma(
        collection_name="domain_knowledge",
        embedding_function=embeddings,
        persist_directory=persist_directory,
    )
    retriever = vector_store.as_retriever(
        search_type="mmr",
        search_kwargs={"k": 4, "fetch_k": 20, "lambda_mult": 0.5},
    )
    query_results = retriever.get_relevant_documents(task)
    return query_results