from datetime import datetime

async def get_fk_flag_tool(collectSuccessRate: str, startTime: str, duration: str, businessLineId: str):
    """判断任务是否有风控
    collectSuccessRate 采集成功率
    startTime 采集任务启动时间
    duration 采集任务持续时间
    businessLineId 业务线id"""
    current_time = datetime.now().strftime("%H:%M")
    # current_time = "12:55"  # 指定查询时间，实验用
    current_time = datetime.strptime(current_time, "%H:%M")
    start_time = datetime.strptime(startTime, "%H:%M")
    time_diff = (current_time - start_time).total_seconds() / 60

    target_sucess_rate = min(round(int(time_diff) / int(duration), 4), 1) * 100
    success_diff = float(collectSuccessRate) - target_sucess_rate
    if success_diff <= -20:
        return f"采集返回慢，可能有风控。目标成功率{target_sucess_rate}, 实际成功率{collectSuccessRate}"
    elif -20 < success_diff < 0:
        return f"采集返回慢，持续观察。目标成功率{target_sucess_rate}, 实际成功率{collectSuccessRate}"
    elif datetime.now().hour >= 19 and float(collectSuccessRate) < 50 and businessLineId in (1006, 1007):
        return f"存在风控。目标成功率{target_sucess_rate}, 实际成功率{collectSuccessRate}"
    else:
        return f"没有风控。目标成功率{target_sucess_rate}, 实际成功率{collectSuccessRate}"